#!/usr/bin/env node

/**
 * Test script to verify that the ErrorUtils polyfill and react-native-exception-handler
 * work correctly without throwing "Cannot read property 'setGlobalHandler' of undefined" error.
 */

console.log('Testing ErrorUtils polyfill and react-native-exception-handler integration...\n');

// Simulate the polyfill setup from index.ts
if (!global.ErrorUtils) {
    console.log('✓ Setting up ErrorUtils polyfill...');
    global.ErrorUtils = {
        setGlobalHandler: (handler) => {
            global.__globalErrorHandler = handler;
            console.log('✓ setGlobalHandler called successfully');
        },
        getGlobalHandler: () => {
            return global.__globalErrorHandler;
        },
        reportError: (error) => {
            if (global.__globalErrorHandler) {
                global.__globalErrorHandler(error, false);
            }
        },
        reportFatalError: (error) => {
            if (global.__globalErrorHandler) {
                global.__globalErrorHandler(error, true);
            }
        }
    };
} else {
    console.log('✓ ErrorUtils already exists');
}

// Test that ErrorUtils is properly defined
console.log('✓ ErrorUtils object:', typeof global.ErrorUtils);
console.log('✓ setGlobalHandler method:', typeof global.ErrorUtils.setGlobalHandler);

// Test importing react-native-exception-handler (simulate)
try {
    console.log('\n--- Simulating react-native-exception-handler import ---');
    
    // This simulates what happens in react-native-exception-handler/index.js line 15
    if (global.ErrorUtils && typeof global.ErrorUtils.setGlobalHandler === 'function') {
        console.log('✓ ErrorUtils.setGlobalHandler is available');
        
        // Test calling setGlobalHandler
        const testHandler = (error, isFatal) => {
            console.log('Test handler called:', error, isFatal);
        };
        
        global.ErrorUtils.setGlobalHandler(testHandler);
        console.log('✓ Successfully called ErrorUtils.setGlobalHandler');
        
        // Test that the handler was set
        const retrievedHandler = global.ErrorUtils.getGlobalHandler();
        if (retrievedHandler === testHandler) {
            console.log('✓ Handler was correctly set and retrieved');
        } else {
            console.log('✗ Handler was not correctly set');
        }
        
    } else {
        console.log('✗ ErrorUtils.setGlobalHandler is not available');
        process.exit(1);
    }
    
    console.log('\n--- Testing error reporting ---');
    
    // Test error reporting
    global.ErrorUtils.reportError('Test error');
    console.log('✓ Successfully called reportError');
    
    global.ErrorUtils.reportFatalError('Test fatal error');
    console.log('✓ Successfully called reportFatalError');
    
    console.log('\n🎉 All tests passed! The ErrorUtils polyfill is working correctly.');
    console.log('The "Cannot read property \'setGlobalHandler\' of undefined" error should be fixed.');
    
} catch (error) {
    console.error('\n❌ Test failed:', error.message);
    console.error('Stack trace:', error.stack);
    process.exit(1);
}
