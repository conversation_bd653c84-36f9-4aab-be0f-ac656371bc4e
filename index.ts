// AppRegistry.registerComponent(...);

// Initialize ErrorUtils polyfill FIRST before any other imports
// This ensures it's available when react-native-exception-handler is imported
if (!global.ErrorUtils) {
    global.ErrorUtils = {
        setGlobalHandler: (handler: (error: any, isFatal: boolean) => void) => {
            global.__globalErrorHandler = handler;
        },
        getGlobalHandler: () => {
            return global.__globalErrorHandler;
        },
        reportError: (error: any) => {
            if (global.__globalErrorHandler) {
                global.__globalErrorHandler(error, false);
            }
        },
        reportFatalError: (error: any) => {
            if (global.__globalErrorHandler) {
                global.__globalErrorHandler(error, true);
            }
        }
    };
}

// Ensure ErrorUtils is properly defined on the global object
if (typeof global.ErrorUtils === 'undefined') {
    console.error('ErrorUtils polyfill failed to initialize properly');
}

import TurboLogger from '@mattermost/react-native-turbo-log';
import { I18nManager, LogBox, Platform, UIManager } from 'react-native';
import ViewReactNativeStyleAttributes from 'react-native/Libraries/Components/View/ReactNativeStyleAttributes';
import 'react-native-gesture-handler';
import { Navigation } from 'react-native-navigation';

import { start } from './app/init/app';
import setFontFamily from './app/utils/font_family';
import { logInfo } from './app/utils/log';



I18nManager.forceRTL(true);

declare const global: { HermesInternal: null | {} };

// Add scaleY back to work around its removal in React Native 0.70.
ViewReactNativeStyleAttributes.scaleY = true;

TurboLogger.configure({
    dailyRolling: false,
    logToFile: !__DEV__,
    maximumFileSize: 1024 * 1024,
    maximumNumberOfFiles: 2,
});

if (__DEV__) {
    /*    LogBox.ignoreLogs([
            'new NativeEventEmitter',
        ]);
    
        // Ignore all notifications if running e2e
        const isRunningE2e = RUNNING_E2E === 'true';
        logInfo(`RUNNING_E2E: ${RUNNING_E2E}, isRunningE2e: ${isRunningE2e}`);
        if (isRunningE2e) {
        }
       */
    LogBox.ignoreAllLogs(true);

}

setFontFamily();

if (global.HermesInternal) {
    // Polyfills required to use Intl with Hermes engine
    require('@formatjs/intl-getcanonicallocales/polyfill-force');
    require('@formatjs/intl-locale/polyfill-force');
    require('@formatjs/intl-pluralrules/polyfill-force');
    require('@formatjs/intl-numberformat/polyfill-force');
    require('@formatjs/intl-datetimeformat/polyfill-force');
    require('@formatjs/intl-datetimeformat/add-golden-tz');
    require('@formatjs/intl-listformat/polyfill-force');
}

if (Platform.OS === 'android') {
    const ShareExtension = require('share_extension/index.tsx').default;
    const AppRegistry = require('react-native/Libraries/ReactNative/AppRegistry');
    // AppRegistry.registerComponent(...);
    AppRegistry.registerComponent('MattermostShare', () => ShareExtension);
    if (UIManager.setLayoutAnimationEnabledExperimental) {
        UIManager.setLayoutAnimationEnabledExperimental(true);
    }
}

Navigation.events().registerAppLaunchedListener(async () => {
    start();
});
